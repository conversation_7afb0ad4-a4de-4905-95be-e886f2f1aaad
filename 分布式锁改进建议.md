# JMT Cloud项目分布式锁改进建议

## 1. 现状分析

### 锁实现机制
- 项目主要使用`java.util.concurrent.locks.ReentrantLock`本地锁
- 仅在`PaymentService`中的`processOrder`方法使用了本地锁
- 项目集成了Redis，但**没有实现任何基于Redis的分布式锁**
- 缺乏统一的分布式锁框架和工具类

## 2. 存在的严重问题

### 2.1 本地锁在分布式环境下失效

```java
// PaymentService.java:158 - 问题代码
if(lock.tryLock()){
    try {
        // 处理订单逻辑
    } finally {
        lock.unlock();
    }
}
```

**问题：** 在微服务分布式部署时，`ReentrantLock`只能保证单个JVM实例内的线程安全，无法防止多个服务实例同时处理同一订单。

### 2.2 缺乏防重复处理机制
- 支付回调、订单处理等关键业务逻辑没有分布式锁保护
- 可能导致：重复扣费、订单状态异常、数据不一致

### 2.3 竞态条件风险
- 用户注册、余额操作、积分操作等并发场景缺乏保护
- `UaaUserService.registerUser()`方法存在并发创建重复账号的风险

## 3. 改进建议

### 3.1 引入Redisson分布式锁框架

#### 1. 添加依赖

```xml
<dependency>
    <groupId>org.redisson</groupId>
    <artifactId>redisson-spring-boot-starter</artifactId>
    <version>3.24.3</version>
</dependency>
```

#### 2. 创建分布式锁工具类

```java
@Component
public class DistributedLockUtil {

    @Resource
    private RedissonClient redissonClient;

    /**
     * 获取分布式锁
     * @param lockKey 锁key
     * @param waitTime 等待时间(秒)
     * @param leaseTime 锁持有时间(秒)
     */
    public RLock getLock(String lockKey, long waitTime, long leaseTime) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean acquired = lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
            return acquired ? lock : null;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }

    /**
     * 执行带锁的业务逻辑
     */
    public <T> T executeWithLock(String lockKey, long waitTime, long leaseTime,
                                 Supplier<T> business) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            boolean acquired = lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
            if (acquired) {
                return business.get();
            } else {
                throw new BusinessException("获取锁失败，请稍后重试");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BusinessException("操作被中断");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
```

### 3.2 关键业务场景改造

#### 1. 订单支付处理（PaymentService）

```java
@Transactional(rollbackFor = Exception.class)
public void processOrder(LklPayNotifyDTO notifyDTO) {
    String lockKey = "order:process:" + notifyDTO.getOrderNo();

    distributedLockUtil.executeWithLock(lockKey, 3, 10, () -> {
        // 处理订单逻辑
        String response = cmFarmingFoodFeignClient.getOrderByOrderNo(notifyDTO.getOrderNo());
        CmFarmingFoodOrder order = ResponseUtil.getData(response, CmFarmingFoodOrder.class);

        if (order == null) {
            throw new BusinessException("订单不存在");
        }
        if (PayStatusEnum.SUCCESS.getCode().equals(order.getOrderStatus())) {
            throw new BusinessException("订单已完成");
        }

        // 更新订单状态和记录支付流水
        // ...
        return null;
    });
}
```

#### 2. 用户注册（UaaUserService）

```java
@Transactional
public UaaUser registerUser(UaaUserDTO userDTO) {
    String lockKey = "user:register:" + userDTO.getTelPhone();

    return distributedLockUtil.executeWithLock(lockKey, 2, 5, () -> {
        // 检查是否已存在账号
        UaaUser existingUser = uaaUserDao.existsUaa(userDTO);
        if (existingUser != null) {
            return existingUser;
        }

        // 创建新用户逻辑
        // ...
        return user;
    });
}
```

#### 3. 余额/积分操作

```java
@Transactional(rollbackFor = Exception.class)
public void balancePay(BalancePayDto dto) {
    String lockKey = "balance:pay:" + dto.getUaaId();

    distributedLockUtil.executeWithLock(lockKey, 3, 10, () -> {
        // 验证支付密码
        validatePayPassword(dto.getUaaId(), dto.getPayPassword());

        // 扣减余额
        String decreaseResult = uaaFeignClient.decreaseBalance(dto.getUaaId(), dto.getBalance());
        if (decreaseResult != null && (decreaseResult.contains("失败") || decreaseResult.contains("余额不足"))) {
            throw new RuntimeException("余额扣减失败,余额不足");
        }

        // 其他逻辑...
        return null;
    });
}
```

### 3.3 注解式分布式锁

#### 创建锁注解

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributedLock {
    String key();
    long waitTime() default 3;
    long leaseTime() default 10;
    String errorMessage() default "操作频繁，请稍后重试";
}
```

#### AOP切面实现

```java
@Aspect
@Component
public class DistributedLockAspect {

    @Resource
    private DistributedLockUtil distributedLockUtil;

    @Around("@annotation(distributedLock)")
    public Object around(ProceedingJoinPoint point, DistributedLock distributedLock) throws Throwable {
        String lockKey = parseLockKey(distributedLock.key(), point);

        return distributedLockUtil.executeWithLock(
            lockKey,
            distributedLock.waitTime(),
            distributedLock.leaseTime(),
            () -> {
                try {
                    return point.proceed();
                } catch (Throwable e) {
                    throw new RuntimeException(e);
                }
            }
        );
    }

    private String parseLockKey(String keyExpression, ProceedingJoinPoint point) {
        // 解析SpEL表达式，支持参数占位符
        // 实现省略...
        return keyExpression;
    }
}
```

#### 使用示例

```java
@DistributedLock(key = "user:register:#{#userDTO.telPhone}", waitTime = 2, leaseTime = 5)
public UaaUser registerUser(UaaUserDTO userDTO) {
    // 业务逻辑
}

@DistributedLock(key = "order:process:#{#notifyDTO.orderNo}", waitTime = 3, leaseTime = 10)
public void processOrder(LklPayNotifyDTO notifyDTO) {
    // 业务逻辑
}
```

## 4. 具体需要改造的方法

### 高优先级改造
1. **PaymentService.processOrder()** - 订单处理防重复
2. **PaymentService.balancePay()** - 余额支付
3. **PaymentService.pointsPay()** - 积分支付
4. **UaaUserService.registerUser()** - 用户注册防重复
5. **InvestorService.assignEq()** - 设备分配

### 中优先级改造
1. **UaaUserService.changePassword()** - 密码修改
2. **UaaUserService.forgetPassword()** - 密码重置
3. **InvestorService.audit()** - 审核操作
4. **InvestorService.freeze()** - 冻结操作

### 低优先级改造
1. 各种统计类操作
2. 缓存更新操作
3. 定时任务中的批量处理

## 5. 实施步骤

### 第一阶段：基础设施搭建
1. 添加Redisson依赖
2. 创建分布式锁工具类
3. 编写单元测试验证锁功能

### 第二阶段：核心业务改造
1. 改造支付相关方法
2. 改造用户注册方法
3. 改造设备分配方法

### 第三阶段：注解式锁实现
1. 开发注解和AOP切面
2. 重构已改造的方法使用注解
3. 扩展到其他业务方法

### 第四阶段：监控和优化
1. 添加锁使用监控
2. 性能调优
3. 异常处理完善

## 6. 注意事项

### 6.1 锁设计原则
- **锁的粒度要合适**：避免过粗影响性能，避免过细增加复杂度
- **设置合理的超时时间**：防止死锁，避免长时间等待
- **考虑锁的可重入性**：同一线程可以多次获取同一把锁
- **异常处理**：确保在异常情况下锁能正确释放

### 6.2 性能考虑
- 避免在高频操作中使用分布式锁
- 合理设置锁的等待时间和持有时间
- 监控锁的竞争情况和性能指标
- 考虑使用读写锁优化读多写少场景

### 6.3 运维监控
- 监控锁的获取成功率
- 监控锁的平均持有时间
- 设置锁超时告警
- 记录锁竞争激烈的业务场景

### 6.4 测试策略
- 单元测试：验证锁的基本功能
- 集成测试：验证分布式环境下的锁效果
- 压力测试：验证高并发场景下的性能
- 故障测试：验证Redis故障时的降级处理

## 7. 预期收益

### 7.1 数据一致性提升
- 消除重复订单处理
- 防止重复用户注册
- 确保余额操作原子性

### 7.2 系统稳定性增强
- 减少并发异常
- 提高业务逻辑可靠性
- 降低数据不一致风险

### 7.3 开发效率提升
- 统一的锁使用方式
- 注解式开发简化代码
- 减少并发问题调试时间

通过以上改进，JMT Cloud项目将具备完善的分布式锁机制，显著提升系统在分布式环境下的数据一致性和并发安全性。