// Prettier配置文档：https://prettier.io/docs/en/options.html
module.exports = {
	printWidth: 180,
	tabWidth: 2,
	useTabs: true,
	semi: false,
	singleQuote: true,
	proseWrap: 'preserve',
	arrowParens: 'avoid',
	bracketSpacing: true,
	bracketSameLine: false,
	jsxBracketSameLine: false,
	jsxSingleQuote: false,
	endOfLine: 'auto',
	htmlWhitespaceSensitivity: 'ignore',
	ignorePath: '.prettierignore',
	requireConfig: false,
	trailingComma: 'es5',
	requirePragma: false,
	quoteProps: 'as-needed',
	vueIndentScriptAndStyle: false,
	eslintIntegration: true,

	//自定义文件后缀对应的parser
	parsers: {
		'.nvue': 'vue',
		'.ux': 'vue',
		'.uvue': 'vue',
		'.uts': 'typescript',
	},
}