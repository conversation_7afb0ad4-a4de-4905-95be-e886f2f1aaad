{
    "name" : "聚合伙",
    "appid" : "__UNI__1373511",
    "description" : "",
    "versionName" : "1.4.10",
    "versionCode" : 125,
    "transformPx" : false,
	"sassImplementationName": "node-sass",
    "app-plus" : {
        "nvueCompiler" : "uni-app",
        "compatible" : {
            "ignoreVersion" : true //true表示忽略版本检查提示框，HBuilderX1.9.0及以上版本支持  
        },
        "compilerVersion" : 3,
        "modules" : {
            "VideoPlayer" : {},
            "Camera" : {},
            "Maps" : {},
            "Geolocation" : {},
            "Barcode" : {}
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_MEDIA_IMAGES\" />",
                    "<uses-permission android:name=\"android.permission.READ_MEDIA_VIDEO\" />",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>",
                    "<uses-permission android:name=\"android.permission.SEND_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ]
            },
            "ios" : {
                "dSYMs" : false
            },
            "sdkConfigs" : {
                "ad" : {},
                "payment" : {},
                "maps" : {
                    "amap" : {
                        "name" : "amapd6LGjllS",
                        "appkey_ios" : "afa5a145bda924acb44b368aa03c8aa6",
                        "appkey_android" : "afa5a145bda924acb44b368aa03c8aa6"
                    }
                },
                "geolocation" : {
                    "amap" : {
                        "name" : "amapd6LGjllS",
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "afa5a145bda924acb44b368aa03c8aa6",
                        "appkey_android" : "afa5a145bda924acb44b368aa03c8aa6"
                    }
                }
            },
            "splashscreen" : {
                "android" : {
                    "hdpi" : "",
                    "xhdpi" : "",
                    "xxhdpi" : ""
                },
                "androidStyle" : "default"
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        },
        "splashscreen" : {
            "waiting" : false
        }
    },
    // "nativePlugins" : {
    //     "Fvv-UniWifiHelper" : {
    //         "__plugin_info__" : {
    //             "name" : "安卓wifi助手 Fvv-UniWifiHelper",
    //             "description" : "安卓平台的wifi助手，开关热点，连接wifi，局域网设备，端口扫描  ",
    //             "platforms" : "Android",
    //             "url" : "https://ext.dcloud.net.cn/plugin?id=2525",
    //             "android_package_name" : "com.jmt.jhh",
    //             "s_bundle_id" : "",
    //             "isCloud" : true,
    //             "bought" : 1,
    //             "pid" : "2525",
    //             "parameters" : {}
    //         }
    //     }
    // }
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wx00c445ef65a61cb3",
        "setting" : {
            "urlCheck" : true,
            "minified" : true,
            "postcss" : true,
            "es6" : true
        },
        "permission" : {}
    },
    "locale" : "zh-Hans",
    "fallbackLocale" : "zh-Hans"
}
