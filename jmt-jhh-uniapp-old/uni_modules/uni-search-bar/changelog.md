## 1.2.3（2022-05-24）
- 新增 readonly 属性，组件只读
## 1.2.2（2022-05-06）
- 修复  vue3 input 事件不生效的bug
## 1.2.1（2022-05-06）
- 修复 多余代码导致的bug
## 1.2.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-search-bar](https://uniapp.dcloud.io/component/uniui/uni-search-bar)
## 1.1.2（2021-08-30）
- 修复 value 属性与 modelValue 属性不兼容的Bug
## 1.1.1（2021-08-24）
- 新增 支持国际化
## 1.1.0（2021-07-30）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.0.9（2021-05-12）
- 新增 项目示例地址
## 1.0.8（2021-04-21）
- 优化 添加依赖 uni-icons, 导入后自动下载依赖
## 1.0.7（2021-04-15）
- uni-ui 新增 uni-search-bar 的 focus 事件

## 1.0.6（2021-02-05）
- 优化 组件引用关系，通过uni_modules引用组件

## 1.0.5（2021-02-05）
- 调整为uni_modules目录规范
- 新增 支持双向绑定
- 更改 input 事件的返回值，e={value:Number} --> e=value
- 新增 支持图标插槽
- 新增 支持 clear、blur 事件
- 新增 支持 focus 属性
- 去掉组件背景色
