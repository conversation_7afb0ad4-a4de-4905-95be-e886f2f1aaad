module.exports = {
  // 设置服务器相关的配置
  devServer: {
    // 代理配置
    proxy: {
		'^/BaseUrl': {
			// 被代理的地址
			target: 'https://gateway.jmingt.com/jmt-commission/', 
			//target: 'http://************:8080/jmt-commission/',
			changeOrigin: true, // 是否开启代理   
			pathRewrite: { // 路径重写
				'^/BaseUrl': ''
			}
		},
		'^/UaaUrl': {
			// 被代理的地址
			target: 'https://gateway.jmingt.com/',
			//target: 'http://************:8080/',
			changeOrigin: true, // 是否开启代理   
			pathRewrite: { // 路径重写
				'^/UaaUrl': ''
			}
		},   
		// 请求地址以/api的开关时候,就触发代理机制
		'^/app': {
			// 被代理的地址
			target: 'http://4436wu4826.zicp.vip', 
			changeOrigin: true, // 是否开启代理   
			pathRewrite: { // 路径重写
		      '^/app': ''
			}
		},
    }
  }
}