<wxs src="../wxs/utils.wxs" module="utils" />

<view class="van-checkbox custom-class">
  <view class="van-checkbox__icon-wrap" bindtap="toggle">
    <slot wx:if="{{ useIconSlot }}" name="icon" />
    <van-icon
      wx:else
      name="success"
      size="0.8em"
      class="{{ utils.bem('checkbox__icon', [shape, { disabled, checked: value }]) }}"
      style="font-size: {{ utils.addUnit(iconSize) }};{{ checkedColor && value && !disabled ? 'border-color:' + checkedColor + '; background-color:' + checkedColor : '' }}"
      custom-class="icon-class"
      custom-style="line-height: 1.25em;"
    />
  </view>
  <view class="label-class {{ utils.bem('checkbox__label', [labelPosition, { disabled }]) }}" bindtap="onClickLabel">
    <slot />
  </view>
</view>
