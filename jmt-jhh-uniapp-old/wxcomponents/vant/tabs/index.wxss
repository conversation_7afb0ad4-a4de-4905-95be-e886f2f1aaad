@import '../common/index.wxss';

.van-tabs {
	position: relative;
	-webkit-tap-highlight-color: transparent
}

.van-tabs__wrap {
	display: -webkit-flex;
	display: flex;
	overflow: hidden
}

.van-tabs__wrap--scrollable .van-tab {
	-webkit-flex: 0 0 22%;
	flex: 0 0 22%
}

.van-tabs__scroll {
	background-color: #fff;
	background-color: var(--tabs-nav-background-color, #fff)
}

.van-tabs__scroll--line {
	box-sizing: initial;
	height: calc(100% + 15px)
}

.van-tabs__scroll--card {
	margin: 0 16px;
	margin: 0 var(--padding-md, 16px)
}

.van-tabs__nav {
	position: relative;
	display: -webkit-flex;
	display: flex;
	-webkit-user-select: none;
	user-select: none
}

.van-tabs__nav--card {
	box-sizing: border-box;
	height: 30px;
	height: var(--tabs-card-height, 30px);
	border: 1px solid #ee0a24;
	border: var(--border-width-base, 1px) solid var(--tabs-default-color, #ee0a24);
	border-radius: 2px;
	border-radius: var(--border-radius-sm, 2px)
}

.van-tabs__nav--card .van-tab {
	color: #ee0a24;
	color: var(--tabs-default-color, #ee0a24);
	line-height: 28px;
	line-height: calc(var(--tabs-card-height, 30px) - 2*var(--border-width-base, 1px));
	border-right: 1px solid #ee0a24;
	border-right: var(--border-width-base, 1px) solid var(--tabs-default-color, #ee0a24)
}

.van-tabs__nav--card .van-tab:last-child {
	border-right: none
}

.van-tabs__nav--card .van-tab.van-tab--active {
	color: #fff;
	color: var(--white, #fff);
	background-color: #ee0a24;
	background-color: var(--tabs-default-color, #ee0a24)
}

.van-tabs__nav--card .van-tab--disabled {
	color: #c8c9cc;
	color: var(--tab-disabled-text-color, #c8c9cc)
}

.van-tabs__line {
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 1;
	height: 4px;
	height: var(--tabs-bottom-bar-height, 4px);
	border-radius: 3px;
	border-radius: var(--tabs-bottom-bar-height, 3px);
	background-color: #3d83f5;
	background-color: var(--tabs-bottom-bar-color, #3d83f5)
}

.van-tabs__track {
	position: relative;
	width: 100%;
	height: 100%
}

.van-tabs__track--animated {
	display: -webkit-flex;
	display: flex;
	transition-property: -webkit-transform;
	transition-property: transform;
	transition-property: transform, -webkit-transform
}

.van-tabs__content {
	overflow: hidden
}

.van-tabs--line .van-tabs__wrap {
	height: 44px;
	height: var(--tabs-line-height, 44px)
}

.van-tabs--card .van-tabs__wrap {
	height: 30px;
	height: var(--tabs-card-height, 30px)
}

.van-tab {
	position: relative;
	-webkit-flex: 1;
	flex: 1;
	box-sizing: border-box;
	min-width: 0;
	padding: 0 5px;
	text-align: center;
	cursor: pointer;
	color: #646566;
	color: var(--tab-text-color, #646566);
	font-size: 14px;
	font-size: var(--tab-font-size, 14px);
	line-height: 44px;
	line-height: var(--tabs-line-height, 44px)
}

.van-tab--active {
	font-weight: 500;
	font-weight: var(--font-weight-bold, 500);
	color: #323233;
	color: var(--tab-active-text-color, #323233)
}

.van-tab--disabled {
	color: #c8c9cc;
	color: var(--tab-disabled-text-color, #c8c9cc)
}

.van-tab--complete {
	-webkit-flex: 1 0 auto !important;
	flex: 1 0 auto !important
}

.van-tab__title__info {
	position: relative !important;
	top: -1px !important;
	display: inline-block;
	-webkit-transform: translateX(0) !important;
	transform: translateX(0) !important
}
