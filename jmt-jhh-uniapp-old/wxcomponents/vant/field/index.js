import { VantComponent } from '../common/component';
import { getSystemInfoSync } from '../common/utils';
VantComponent({
    field: true,
    classes: ['input-class', 'right-icon-class'],
    props: {
        size: String,
        icon: String,
        label: String,
        error: <PERSON><PERSON>an,
        fixed: Boolean,
        focus: <PERSON><PERSON>an,
        center: <PERSON><PERSON>an,
        isLink: Boolean,
        leftIcon: String,
        rightIcon: String,
        disabled: <PERSON><PERSON>an,
        autosize: <PERSON><PERSON><PERSON>,
        readonly: <PERSON><PERSON><PERSON>,
        required: <PERSON><PERSON><PERSON>,
        password: <PERSON><PERSON>an,
        iconClass: String,
        clearable: <PERSON><PERSON>an,
        clickable: <PERSON><PERSON>an,
        inputAlign: String,
        placeholder: String,
        customStyle: String,
        confirmType: String,
        confirmHold: Boolean,
        holdKeyboard: Bo<PERSON>an,
        errorMessage: String,
        arrowDirection: String,
        placeholderStyle: String,
        errorMessageAlign: String,
        selectionEnd: {
            type: Number,
            value: -1
        },
        selectionStart: {
            type: Number,
            value: -1
        },
        showConfirmBar: {
            type: <PERSON><PERSON>an,
            value: true
        },
        adjustPosition: {
            type: <PERSON><PERSON><PERSON>,
            value: true
        },
        cursorSpacing: {
            type: Number,
            value: 50
        },
        maxlength: {
            type: Number,
            value: -1
        },
        type: {
            type: String,
            value: 'text'
        },
        border: {
            type: <PERSON><PERSON>an,
            value: true
        },
        titleWidth: {
            type: String,
            value: '90px'
        }
    },
    data: {
        focused: false,
        system: getSystemInfoSync().system.split(' ').shift().toLowerCase()
    },
    methods: {
        onInput(event) {
            const { value = '' } = event.detail || {};
            this.setData({ value });
            wx.nextTick(() => {
                this.emitChange(value);
            });
        },
        onFocus(event) {
            this.setData({ focused: true });
            this.$emit('focus', event.detail);
        },
        onBlur(event) {
            this.setData({ focused: false });
            this.$emit('blur', event.detail);
        },
        onClickIcon() {
            this.$emit('click-icon');
        },
        onClear() {
            this.setData({ value: '' });
            wx.nextTick(() => {
                this.emitChange('');
                this.$emit('clear', '');
            });
        },
        onConfirm() {
            this.$emit('confirm', this.data.value);
        },
        emitChange(value) {
            this.$emit('input', value);
            this.$emit('change', value);
        },
        noop() { }
    }
});
