# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

JMT Cloud is a microservices-based cloud platform built with Spring Cloud Alibaba, featuring multiple business modules and a Vue.js admin frontend. The project supports agricultural supply chain management, equipment management, profit sharing, and other business functions.

## Project Structure

### Backend (Java Spring Cloud)
- **jmt-gateway**: API Gateway using Spring Cloud Gateway
- **jmt-auth**: Authentication service with JWT support
- **jmt-common**: Shared utilities and common components
- **jmt-base**: Base configurations and foundation classes
- **jmt-micro-services/**: Business microservices
  - **jmt-admin**: System administration service
  - **jmt-uaa**: User authentication and authorization
  - **jmt-cm**: Customer/Community management service
  - **jmt-eq**: Equipment management service
  - **jmt-profit**: Profit sharing and financial management
  - **jmt-tg**: Media and content management
  - **jmt-jhh**: Joint household service
  - **jmt-zg**: General business service
  - **jmt-bd**: Business data service

### Frontend
- **jmt-front/jmt-admin-vue**: Vue 3 + Vite admin dashboard with Element Plus
- **jmt-front/jmt-cm-uniapp**: UniApp mobile application
- **jmt-front/jmt-jhh-uniapp-old**: Legacy UniApp application

## Technology Stack

### Backend
- Java 1.8
- Spring Boot 2.3.12.RELEASE
- Spring Cloud Hoxton.SR12
- Spring Cloud Alibaba 2.2.8.RELEASE
- Nacos (Service discovery and configuration)
- Spring Security + JWT
- MyBatis
- MySQL + Druid connection pool
- Redis
- Log4j2

### Frontend (Admin Vue)
- Vue 3.5+ with Composition API
- Vite 6.1+ build tool
- TypeScript 5.6+
- Element Plus UI framework
- Pinia state management
- Vue Router 4+
- ESLint + Prettier + Stylelint
- Husky + lint-staged for git hooks

## Development Commands

### Backend (Maven)
```bash
# Build entire project
mvn clean package

# Build specific module
cd jmt-micro-services/jmt-admin
mvn clean package

# Run specific service
mvn spring-boot:run

# Skip tests during build
mvn clean package -DskipTests
```

### Frontend (Admin Vue)
Navigate to `jmt-front/jmt-admin-vue` first:

```bash
# Development server
npm run dev

# Production build
npm run build

# Development build
npm run build:dev

# Linting
npm run lint
npm run fix
npm run lint:prettier
npm run lint:stylelint

# Git commit (with commitizen)
npm run commit
```

## Code Architecture

### Microservices Communication
- Services communicate via OpenFeign clients
- API Gateway routes external requests to appropriate services
- Nacos handles service discovery and load balancing
- Shared authentication through JWT tokens

### Database Layer
- Each microservice has its own database schema
- MyBatis XML mappers located in `src/main/resources/mapper/`
- Database connection pools managed by Druid

### Frontend Architecture
- Component-based Vue 3 architecture with TypeScript
- Centralized state management with Pinia
- Modular routing with lazy-loaded components
- API communication through Axios with interceptors
- Element Plus for UI components

### Configuration Management
- Nacos config center for distributed configuration
- Environment-specific properties via Spring profiles
- Bootstrap configuration for service registration

## Key Development Patterns

### Backend
- RESTful API design with consistent response formats
- Service layer pattern with DAO/Entity separation
- AOP for cross-cutting concerns (logging, security)
- Exception handling through global exception handlers

### Frontend
- Composition API with `<script setup>` syntax
- TypeScript interfaces for type safety
- Composables for reusable logic
- Auto-import for Vue APIs and components

## Testing

Currently no test automation commands are configured. Manual testing is performed through:
- Backend: Direct API testing via tools like Postman
- Frontend: Browser testing in development mode

## Deployment Notes

- Services designed for containerized deployment
- Configuration externalized through Nacos
- Database migrations handled manually
- Static assets built and served separately

## Linting and Code Quality

The admin frontend has comprehensive linting setup:
- ESLint for JavaScript/TypeScript code quality
- Prettier for code formatting
- Stylelint for CSS/SCSS consistency
- Pre-commit hooks ensure code quality before commits

Always run `npm run lint` and `npm run fix` before committing frontend changes.